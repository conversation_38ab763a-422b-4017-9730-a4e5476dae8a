<template>
  <div class="app-container">
    <el-row :gutter="10" style="height: 380px; margin-bottom: 10px">
      <el-col :span="12" style="height: 100%">
        <el-card>
          <div slot="header">服务器列表</div>
          <MsCrud
            ref="crudRef"
            :columns="serverColumns"
            :isShowAdd="false"
            :isShowOperate="false"
            :isRadio="true"
            api="system/etlLog/serverList"
            :processListParams="handleProcessListParams"
            @selectionChange="handleSelectionChange"
          >
            <template #networkStatus="{ row }">
              <img
                style="width: 22px; height: 22px"
                src="../../../assets/images/disease/link-sucess.svg"
                v-if="row.networkStatus == '1'"
              />
              <img
                style="width: 22px; height: 22px"
                src="../../../assets/images/disease/link-error.svg"
                v-else
              />
            </template>
            <template #flag="{ row }">
              <i class="el-icon-check" v-if="currentRowKey == row.serverId"></i>
            </template>
          </MsCrud>
        </el-card>
      </el-col>
      <el-col :span="12" style="height: 100%">
        <el-card>
          <div slot="header">服务器详情</div>
          <div class="server-detail">
            <div class="s-d-left">
              <div class="l-item">
                <img
                  class="server-icon"
                  src="../../../assets/images/disease/cpu.svg"
                />
                <span class="label">CPU：</span>
                <span class="value">{{ currentRow.serverCpu }}</span>
              </div>
              <div class="l-item">
                <img
                  class="server-icon"
                  src="../../../assets/images/disease/neicun.svg"
                />
                <span class="label">内存：</span>
                <span class="value">{{ currentRow.serverMemory }}</span>
              </div>
              <div class="l-item">
                <img
                  class="server-icon"
                  src="../../../assets/images/disease/cipan.svg"
                />
                <span class="label">磁盘：</span>
                <span class="value">{{ currentRow.serverDisk }}</span>
              </div>
              <div class="l-item">
                <img
                  class="server-icon"
                  src="../../../assets/images/disease/gpu.svg"
                />
                <span class="label">GPU：</span>
                <span class="value">{{ currentRow.serverGpu }}</span>
              </div>
            </div>
            <div class="s-d-right">
              <div class="r-left">
                <WaterLevel ref="waterLevelRef" />
                <div style="color: rgba(42, 130, 228, 1)">磁盘使用情况预警</div>
              </div>
              <div class="r-right">
                <div class="r-r-item item1">
                  <span class="label">总容量</span>
                  <span class="value">{{ currentRow.totalCapacity }}GB</span>
                </div>
                <div class="r-r-item item2">
                  <span class="label">已使用</span>
                  <span class="value">{{ currentRow.usedCapacity }}GB</span>
                </div>
                <div class="r-r-item item3">
                  <span class="label">未使用</span>
                  <span class="value">{{ currentRow.freeCapacity }}GB</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="10" style="height: calc(100% - 390px)">
      <el-col :span="24" style="height: 100%">
        <el-card>
          <div slot="header">采集任务列表</div>
          <MsCrud
            ref="taskCrudRef"
            :columns="taskColumns"
            :isShowAdd="false"
            :operateBtns="[]"
            :operateWidth="124"
            api="system/etlLog/taskList"
          >
            <template #taskStatus="{ row }">
              <i
                style="color: #0e932e; font-size: 20px"
                class="el-icon-success"
                v-if="row.taskStatus == '1'"
              ></i>
              <i
                style="color: #d81e06; font-size: 20px"
                class="el-icon-error"
                v-else-if="row.taskStatus == '2'"
              ></i>
              <i
                style="color: #e98f36; font-size: 20px"
                class="el-icon-remove"
                v-else
              ></i>
            </template>
            <template #taskType="{ row }">
              <span>{{ row.taskType == '0' ? '增量' : '全量' }}</span>
            </template>
            <template #operate="{ row }">
              <el-link
                type="primary"
                :underline="false"
                icon="el-icon-s-order"
                @click="showDetails(row)"
                >采集报告明细</el-link
              >
            </template>
          </MsCrud>
        </el-card>
      </el-col>
    </el-row>

    <Details ref="detailsRef" />
  </div>
</template>

<script>
import MsCrud from "@/components/MsCrud";
import WaterLevel from "@/components/MsCharts/waterLevel.vue";
import Details from "./details.vue";
export default {
  components: { MsCrud, WaterLevel, Details },
  data() {
    return {
      serverColumns: [
        {
          aliasName: "服务器名称",
          elementName: "serverName",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "服务器IP",
          elementName: "serverIp",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "服务器用途描述",
          elementName: "usedRemark",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "连接状态",
          elementName: "networkStatus",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "总任务数",
          elementName: "taskNums",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "",
          elementName: "flag",
          htmlType: "input",
          listFlag: "1",
        },
      ],
      currentRowKey: null,
      currentRow: {},
      taskColumns: [
        {
          aliasName: "任务名称",
          elementName: "taskName",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "任务描述",
          elementName: "taskRemark",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "源数据库",
          elementName: "sourceDb",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "目标数据库",
          elementName: "targetDb",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "执行周期",
          elementName: "execPlan",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "执行规则描述",
          elementName: "execRule",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "最近一次执行开始时间",
          elementName: "execStartTime",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "最近一次执行结束时间",
          elementName: "execEndTime",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "最近一次执行总耗时(秒)",
          elementName: "timeConsuming",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "最近一次执行状态",
          elementName: "taskStatus",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "最后一次执行总行数",
          elementName: "syncNums",
          htmlType: "input",
          listFlag: "1",
        },
        {
          aliasName: "任务类型",
          elementName: "taskType",
          htmlType: "input",
          listFlag: "1",
        },
      ],
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.crudRef.reload()
      this.$refs.waterLevelRef.initChart();
    });
  },
  methods: {
    handleProcessListParams(res) {
      this.handleSelectionChange(res.rows[0]);
      return res
    },
    handleSelectionChange(row) {
      this.currentRowKey = row.serverId;
      this.currentRow = row;
      this.$refs.waterLevelRef.initChart(row);
      this.$refs.taskCrudRef.formData.serverId = row.serverId;
      this.$refs.taskCrudRef.reload()
    },
    showDetails(row) {
      this.$refs.detailsRef.show(row);
    },
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  height: 100%;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  .el-card {
    height: 100%;
    ::v-deep {
      .el-card__body {
        height: calc(100% - 44px);
        padding: 0 !important;
      }
    }
  }
  .el-icon-check {
    font-size: 18px;
    font-weight: bold;
    color: #409eff;
  }
  .server-detail {
    display: flex;
    height: 100%;
    padding: 10px;
    .s-d-left {
      width: 40%;
      display: flex;
      flex-direction: column;
      margin-right: 10px;
      .l-item {
        height: 25%;
        display: flex;
        align-items: center;
        font-size: 14px;
        .server-icon {
          width: 24px;
          height: 24px;
          margin-right: 4px;
          vertical-align: middle;
        }
        .label {
          color: #333;
          margin-right: 10px;
        }
        .value {
          color: rgba(42, 130, 228, 1);
        }
      }
    }
    .s-d-right {
      flex: 1;
      display: flex;
      align-items: center;
      .r-left {
        width: 60%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .r-right {
        flex: 1;
        .r-r-item {
          height: 36px;
          font-size: 14px;
          font-weight: bold;
          .label {
            margin-right: 6px;
          }
          .label::before {
            content: "";
            width: 14px;
            height: 14px;
            display: inline-block;
            margin-right: 8px;
            vertical-align: middle;
          }
        }
        .item1 {
          color: rgba(42, 130, 228, 1);
          .label::before {
            background: rgba(42, 130, 228, 1);
          }
        }
        .item2 {
          color: rgba(255, 141, 26, 1);
          .label::before {
            background: rgba(255, 141, 26, 1);
          }
        }
        .item3 {
          color: rgba(0, 186, 173, 1);
          .label::before {
            background: rgba(0, 186, 173, 1);
          }
        }
      }
    }
  }
}
</style>
