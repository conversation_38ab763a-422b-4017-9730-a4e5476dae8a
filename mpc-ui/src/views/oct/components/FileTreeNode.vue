<template>
  <div class="tree-node">
    <div 
      class="node-content" 
      :style="{ paddingLeft: (level * 20 + 10) + 'px' }"
      :class="{ 'is-folder': node.type === 'folder' }"
    >
      <!-- 文件夹展开/收起图标 -->
      <i 
        v-if="node.type === 'folder'"
        class="folder-toggle"
        :class="node.expanded ? 'el-icon-caret-bottom' : 'el-icon-caret-right'"
        @click="$emit('toggle-folder', nodeKey)"
      ></i>
      <span v-else class="file-indent"></span>

      <!-- 文件/文件夹图标 -->
      <i :class="getFileIcon(node.name, node.type)" class="node-icon"></i>

      <!-- 文件/文件夹名称 -->
      <span class="node-name">{{ node.name }}</span>

      <!-- 文件信息（仅文件显示） -->
      <div v-if="node.type === 'file'" class="file-info">
        <span class="file-size">{{ formatFileSize(node.file && node.file.size) }}</span>

        <!-- 进度条 -->
        <div class="progress-container">
          <el-progress
            :percentage="node.progress || 0"
            :status="node.status === 'error' ? 'exception' : (node.status === 'success' ? 'success' : null)"
            :stroke-width="6"
            :show-text="false"
          ></el-progress>
        </div>

        <!-- 状态信息 -->
        <div class="status-info">
          <span
            class="status-text"
            :style="{ color: getStatusColor(node.status) }"
          >
            {{ getStatusText(node) }}
          </span>
        </div>

        <!-- 操作按钮 -->
        <div class="file-actions">
          <el-button
            v-if="node.status === 'error'"
            size="mini"
            type="text"
            @click="$emit('retry-file', node.file)"
          >
            重试
          </el-button>
          <el-button
            size="mini"
            type="text"
            @click="$emit('remove-file', node.file)"
          >
            删除
          </el-button>
        </div>
      </div>


    </div>

    <!-- 子节点（文件夹内容） -->
    <template v-if="node.type === 'folder' && node.expanded && node.children">
      <file-tree-node
        v-for="(childNode, childKey) in node.children"
        :key="childKey"
        :node="childNode"
        :node-key="childKey"
        :level="level + 1"
        @toggle-folder="$emit('toggle-folder', $event)"
        @remove-file="$emit('remove-file', $event)"
        @retry-file="$emit('retry-file', $event)"
      />
    </template>
  </div>
</template>

<script>
export default {
  name: 'FileTreeNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    nodeKey: {
      type: String,
      required: true
    },
    level: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      fileIcons: {
        folder: 'el-icon-folder',
        zip: 'el-icon-box',
        rar: 'el-icon-box',
        '7z': 'el-icon-box',
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        txt: 'el-icon-document',
        jpg: 'el-icon-picture',
        jpeg: 'el-icon-picture',
        png: 'el-icon-picture',
        gif: 'el-icon-picture',
        mp4: 'el-icon-video-camera',
        avi: 'el-icon-video-camera',
        mp3: 'el-icon-headset',
        wav: 'el-icon-headset',
        default: 'el-icon-document'
      }
    }
  },
  methods: {
    getFileIcon(name, type) {
      if (type === 'folder') return this.fileIcons.folder;
      const parts = name.split('.');
      const ext = parts.length > 1 ? parts.pop().toLowerCase() : '';
      return this.fileIcons[ext] || this.fileIcons.default;
    },
    getStatusColor(status) {
      const colors = {
        success: '#67C23A',
        error: '#F56C6C',
        uploading: '#409EFF',
        paused: '#E6A23C',
        waiting: '#909399'
      };
      return colors[status] || colors.waiting;
    },
    formatFileSize(size) {
      if (!size) return '';
      const units = ['B', 'KB', 'MB', 'GB'];
      let index = 0;
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024;
        index++;
      }
      return `${size.toFixed(1)} ${units[index]}`;
    },

    getStatusText(node) {
      if (node.status === 'uploading') {
        let text = `${node.progress || 0}%`;

        // 添加网速信息
        if (node.averageSpeed) {
          text += ` ${this.formatSpeed(node.averageSpeed)}`;
        }

        // 添加预计时间
        if (node.timeRemaining) {
          text += ` 预计还需${this.formatTimeRemaining(node.timeRemaining)}`;
        }

        return text;
      }

      // 错误状态显示详细信息
      if (node.status === 'error') {
        let text = '上传失败';
        if (node.errorDetail) {
          text += `：${node.errorDetail}`;
        }
        return text;
      }

      // 其他状态的文本
      const statusTexts = {
        waiting: '等待上传',
        success: '上传完成',
        paused: '已暂停',
        computing: '计算MD5中...'
      };

      return node.statusText || statusTexts[node.status] || '等待上传';
    },

    formatSpeed(bytesPerSecond) {
      if (!bytesPerSecond) return '';

      const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
      let speed = bytesPerSecond;
      let index = 0;

      while (speed >= 1024 && index < units.length - 1) {
        speed /= 1024;
        index++;
      }

      return `${speed.toFixed(1)} ${units[index]}`;
    },

    formatTimeRemaining(seconds) {
      if (!seconds || seconds <= 0) return '';

      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      if (hours > 0) {
        return `${hours}小时${minutes}分钟`;
      } else if (minutes > 0) {
        return `${minutes}分钟${secs}秒`;
      } else {
        return `${secs}秒`;
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-node {
  .node-content {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f5f5f5;
    }

    &.is-folder {
      font-weight: 500;
    }

    .folder-toggle {
      margin-right: 8px;
      cursor: pointer;
      color: #666;
      transition: transform 0.2s ease;

      &:hover {
        color: #1890ff;
      }
    }

    .file-indent {
      width: 16px;
      margin-right: 8px;
    }

    .node-icon {
      margin-right: 8px;
      font-size: 16px;

      &.el-icon-folder {
        color: #faad14;
      }

      &.el-icon-box {
        color: #722ed1;
      }

      &.el-icon-document {
        color: #1890ff;
      }

      &.el-icon-picture {
        color: #52c41a;
      }

      &.el-icon-video-camera {
        color: #f5222d;
      }

      &.el-icon-headset {
        color: #13c2c2;
      }
    }

    .node-name {
      flex: 1;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 12px;
    }

    .file-info {
      display: flex;
      align-items: center;
      flex: 0 0 auto;

      .file-size {
        font-size: 12px;
        color: #666;
        margin-right: 12px;
        min-width: 60px;
      }

      .progress-container {
        width: 120px;
        margin-right: 12px;
      }

      .status-info {
        min-width: 120px;
        margin-right: 12px;

        .status-text {
          font-size: 12px;
          line-height: 1.4;
        }
      }

      .file-actions {
        display: flex;
        gap: 4px;

        .el-button {
          padding: 4px 8px;
          font-size: 12px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .tree-node .node-content {
    flex-direction: column;
    align-items: flex-start;

    .file-info {
      width: 100%;
      flex-direction: column;
      align-items: flex-start;
      margin-top: 8px;

      .progress-container {
        width: 100%;
        margin: 4px 0;
      }

      .file-actions {
        margin-top: 8px;
      }
    }
  }
}
</style>
