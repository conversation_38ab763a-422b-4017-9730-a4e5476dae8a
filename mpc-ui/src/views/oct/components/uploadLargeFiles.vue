<template>
  <div class="upload-file-wrapper">
    <!-- 上传文件组件 -->
    <uploader class="uploader-app" ref="uploader" :options="options" :autoStart="false" @files-added="handleFilesAdded"
      @file-removed="handleFileRemoved" @file-success="handleFileSuccess" @file-error="handleFileError"
      :file-status-text="fileStatusTextObj">
      <uploader-unsupport></uploader-unsupport>
      <!-- 选择按钮 在这里隐藏 -->
      <uploader-btn class="select-file-btn" :attrs="attrs" ref="uploadBtn">选择文件</uploader-btn>
      <uploader-btn  :attrs="attrs" :directory="true" ref="uploadDirBtn">选择目录</uploader-btn>
      <!-- 拖拽上传 -->
      <uploader-drop>
        <div class="drop-box">
          <i class="el-icon-upload"></i>
          <div class="drop-text">
            <span>将.zip文件拖到此处，或</span>
            <span class="upload-btn" @click="handleClickUploadBtn">点击上传</span>
          </div>
        </div>
      </uploader-drop>
      <!-- 上传列表 -->
      <uploader-list>
        <template v-slot:default="props">
          <div class="file-panel" v-if="props.fileList.length">
            <div class="file-title">
              <span class="title-span">
                上传列表<span class="count">（{{ props.fileList.length }}）</span>
              </span>
            </div>
            <!-- 待上传的文件列表 -->
            <ul class="file-list">
              <li class="file-item" v-for="file in props.fileList" :key="file.id">
                <uploader-file :file="file" :list="true">
                  <template v-slot:default="fileProps">
                    <div class="uploader-file-progress"
                      :class="fileProps.status == 'uploading' ? 'uploader-file-progressing' : ''"
                      :style="{ ...fileProps.progressStyle, background: fileProps.status == 'error' ? '#ffe0e0' : '#e2eeff' }">
                    </div>
                    <div class="uploader-file-info">
                      <div class="uploader-file-name"><i class="uploader-file-icon""></i>{{ fileProps.file.name }}</div>
                      <div class=" uploader-file-size">{{ fileProps.formatedSize }}</div>
                      <div class="uploader-file-status" v-show="fileProps.status == 'paused'">
                        <span>{{ md5Status[file.id] || '等待中' }}</span>
                      </div>
                      <div class="uploader-file-status" v-show="fileProps.status != 'paused'">
                        <span v-show="fileProps.status !== 'uploading'">{{ fileStatusTextObj[fileProps.status] }}</span>
                        <span v-show="fileProps.status === 'uploading'">
                          <span>{{ fileProps.progressStyle.progress }}</span>
                          <em style="margin: 0 4px">{{ fileProps.formatedAverageSpeed }}</em>
                          <i v-show="fileProps.formatedTimeRemaining">预计还需{{ fileProps.formatedTimeRemaining }}</i>
                        </span>
                      </div>
                      <div class="uploader-file-actions">
                        <!-- <span class="uploader-file-pause" @click="onPause(fileProps.file)"></span>
                        <span class="uploader-file-resume" @click="onResume(fileProps.file)"">️</span> -->
                        <span class="uploader-file-retry" @click="onRetry(fileProps.file)"></span>
                        <span class="uploader-file-remove" v-if="!md5Status[file.id]"  @click="onFileRemove(fileProps.file)"></span>
                      </div>
                    </div>
                  </template>
                </uploader-file>
              </li>
            </ul>
          </div>
        </template>
      </uploader-list>
    </uploader>
  </div>
</template>

<script>
import SparkMD5 from 'spark-md5'
import { getToken } from "@/utils/auth";
import { mergeChunks } from "@/api/file/file";
import { createInfo, updateFileInfoStatus } from "@/api/oct/index";
const CHUNK_SIZE = 20 * 1024 * 1024 //  每个分片的大小
export default {
  props: {
    formData: {
      type: Object,
      default: () => { },
    }
  },
  data() {
    return {
      // 上传组件配置项
      options: {
        target: process.env.VUE_APP_BASE_API + "/file/chunk", // 上传文件-目标 URL
        chunkSize: CHUNK_SIZE, //  每个分片的大小
        fileParameterName: 'file', //  上传文件时文件的参数名，默认 file
        maxChunkRetries: 1,
        testChunks: true, //  是否开启分片已存在于服务器的校验
        headers: { Authorization: "Bearer " + getToken() },
        query: this.formData,
        // 服务器分片校验函数，秒传及断点续传基础
        checkChunkUploadedByResponse: (chunk, response) => {
          let result = JSON.parse(response)
          console.log(chunk, 'chunk', result)
          if (result.code && result.code != 200) {
            chunk.uploader.pause()
            // this.$message.error(result.msg)
          }
          // if (result.success) {
          // let data = result.data
          // if (data.skipUpload) {
          //   // 分片已存在于服务器中
          //   return true
          // }
          // return (result.chunkNumbers || []).indexOf(chunk.offset + 1) >= 0
          // }
          return (result.chunkNumbers || []).indexOf(chunk.offset + 1) >= 0
        },
        parseTimeRemaining: function (timeRemaining, parsedTimeRemaining) {
          return parsedTimeRemaining
            .replace(/\syears?/, "年")
            .replace(/\days?/, "天")
            .replace(/\shours?/, "小时")
            .replace(/\sminutes?/, "分钟")
            .replace(/\sseconds?/, "秒");
        },
        // 处理请求参数,一般用于修改参数名字或者删除参数
        processParams: function (params, file, chunk, ieTest) {
          return params
        }
      },
      attrs: {
        accept: '.zip, .rar'
      },
      fileStatusTextObj: {
        success: "上传成功",
        error: "上传失败",
        uploading: "正在上传",
        paused: "",
        waiting: "等待中",
      },
      dropBoxShow: false, //  拖拽上传是否显示
      filesList: [], //  上传的文件列表
      md5Status: {}
    }
  },
  computed: {
    // Uploader	上传组件实例
    uploaderInstance() {
      return this.$refs.uploader.uploader
    },
  },
  methods: {
    handleClickUploadBtn() {
      this.$refs.uploadBtn.$el.click()
    },
    handleFilesAdded(files) {
      this.filesList = [...this.filesList, ...files]
      this.$emit('fileList', this.filesList)
      files.forEach((file) => {
        // this.dropBoxShow = false
        this.computeMD5(file)
      })
    },
    handleFileRemoved(file) {
      this.filesList = this.filesList.filter((item) => item.id != file.id)
      this.$emit('fileList', this.filesList)
      // console.log(this.filesList, 'remove')
    },
    handleFileSuccess(rootFile, file, response) {
      let result = response ? JSON.parse(response) : ''
      console.log(result, 'success', file)

      // code == 205时才执行合并
      if (result.code == 200) return
      const formData = new FormData();
      formData.append("identifier", file.uniqueIdentifier);
      formData.append("filename", file.name);
      formData.append("relativePath", file.relativePath);
      formData.append("totalSize", file.size);
      mergeChunks(formData).then((res) => {
        this.filesList = this.filesList.filter((item) => item.id != file.id)
        this.$emit('fileList', this.filesList)
        if (Notification.permission === 'granted') {
          new Notification('文件上传完成', { body: file.name })
        }
        let params = {
          identifier: file.uniqueIdentifier,
          status: 1,
          fileUrl: res.data.fileUrl
        }
        updateFileInfoStatus(params)
      })
    },

    handleFileError(rootFile, file, response) {
      console.log(rootFile, file, response, 'error')
      // this.$message({
      //   message: response,
      //   type: 'error'
      // })
    },

    async computeMD5(file) {
      // console.log(file,'----')
      return new Promise((resolve, reject) => {
        this.$set(this.md5Status, file.id, '分片处理中 0%')
        let fileReader = new FileReader()
        let blobSlice =
          File.prototype.slice ||
          File.prototype.mozSlice ||
          File.prototype.webkitSlice
        let currentChunk = 0
        let chunks = Math.ceil(file.size / CHUNK_SIZE)
        let spark = new SparkMD5.ArrayBuffer()
        file.pause()
        loadNext()
        fileReader.onload = (e) => {
          spark.append(e.target.result)
          if (currentChunk < chunks) {
            currentChunk++
            loadNext()
            // 实时展示MD5的计算进度
            this.$set(this.md5Status, file.id, `分片处理中 ${((currentChunk / chunks) * 100).toFixed(0)}%`)
          } else {
            let md5 = spark.end()
            this.$set(this.md5Status, file.id, '')
            file.uniqueIdentifier = md5
            let params = {
              identifier: md5,
              manufacturerInfoId: this.formData.manufacturerInfoId,
              deviceTypeId: this.formData.deviceTypeId,
              fileSize: file.size,
              fileName: file.name
            }
            createInfo(params).then(res => {
              if (res.data.status == 1) {
                this.$message.warning('影像已存在，请勿重复上传')
                file.cancel()
              } else {
                file.resume()
                this.md5Status[file.id] = ''
                resolve()
              }
            })
          }
        }
        fileReader.onerror = function () {
          this.$message.error(`文件${file.name}读取出错，请检查该文件`)
          file.cancel()
          resolve()
        }
        function loadNext() {
          let start = currentChunk * CHUNK_SIZE
          let end = start + CHUNK_SIZE >= file.size ? file.size : start + CHUNK_SIZE
          fileReader.readAsArrayBuffer(blobSlice.call(file.file, start, end))
        }
      })
    },
    // 
    async uploadStart() {
      if (this.uploaderInstance.isUploading()) return
      for (let i = 0; i < this.filesList.length; i++) {
        let file = this.filesList[i]
        if (!file.completed) {
          await this.computeMD5(file)
        }
      }
    },
    // 删除
    onFileRemove(file) {
      file.cancel()
    },
    onRetry(file) {
      file.retry()
    },
    onPause(file) {
      file.pause()
    },
    onResume(file) {
      file.resume()
    }
  }
}
</script>

<style lang="scss" scoped>
.uploader-drop {
  border-radius: 4px;
}

.drop-box {
  text-align: center;
  padding: 30px 10px;

  .el-icon-upload {
    font-size: 32px;
    margin-bottom: 4px;
  }

  .upload-btn {
    color: #1890FF;
    cursor: pointer;
  }
}

.file-panel {
  margin-top: 12px;

  .file-title {
    color: #333;
  }
}

::v-deep {

  // .uploader-file-pause,
  // .uploader-file-resume {
  //   display: none !important;
  // }

  // .uploader-file-actions {
  //   width: 5%;
  // }
  .uploader-file-status {
    width: 32%;
  }
}

/* 隐藏上传按钮 */
.select-file-btn {
  display: none;
}
</style>
